# Deadlock API Ingest - Windows Installation Script
# This script downloads and installs the application to run automatically on system startup via Task Scheduler.

# --- Configuration ---
$AppName = "deadlock-api-ingest"
$GithubRepo = "deadlock-api/deadlock-api-ingest"
$AssetKeyword = "windows-latest.exe"

# Installation Paths
$InstallDir = "$env:ProgramFiles\$AppName"
$FinalExecutableName = "$AppName.exe"
$LogFile = "$env:TEMP\${AppName}-install.log"

# Update functionality
$UpdateTaskName = "${AppName}-updater"
$UpdateScriptPath = "$InstallDir\update-checker.ps1"
$UpdateLogFile = "$env:ProgramData\${AppName}\updater.log"
$VersionFile = "$InstallDir\version.txt"
$ConfigFile = "$InstallDir\config.conf"
$BackupDir = "$InstallDir\backup"

# --- Script Setup ---
$ErrorActionPreference = 'Stop'

# --- Helper Functions ---

# Function to write to log and console with color
function Write-Log {
    param(
        [Parameter(Mandatory = $true)]
        [ValidateSet('INFO', 'WARN', 'ERROR', 'SUCCESS')]
        [string]$Level,
        [Parameter(Mandatory = $true)]
        [string]$Message
    )
    $ColorMap = @{ 'INFO' = 'Cyan'; 'WARN' = 'Yellow'; 'ERROR' = 'Red'; 'SUCCESS' = 'Green' }
    $Color = $ColorMap[$Level]
    $Timestamp = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    $LogMessage = "[$Timestamp] [$Level] $Message"
    Write-Host $LogMessage -ForegroundColor $Color
    Add-Content -Path $LogFile -Value $LogMessage
}

# Function to check for Administrator privileges
function Test-IsAdmin {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $isAdmin = (New-Object Security.Principal.WindowsPrincipal $currentUser).IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
    if (-not $isAdmin) {
        Write-Log -Level 'ERROR' "This script requires Administrator privileges. Please re-run as Administrator."
        exit 1
    }
    Write-Log -Level 'INFO' "Running with Administrator privileges."
}

# Function to get the latest release from GitHub
function Get-LatestRelease {
    Write-Log -Level 'INFO' "Fetching latest release from repository: $GithubRepo"
    $ApiUrl = "https://api.github.com/repos/$GithubRepo/releases/latest"
    try {
        $releaseInfo = Invoke-RestMethod -Uri $ApiUrl -UseBasicParsing
    }
    catch {
        Write-Log -Level 'ERROR' "Failed to fetch release information from GitHub API."
        exit 1
    }
    $asset = $releaseInfo.assets | Where-Object { $_.name -like "*$AssetKeyword*" } | Select-Object -First 1
    if (-not $asset) {
        Write-Log -Level 'ERROR' "Could not find a release asset containing the keyword: '$AssetKeyword'"
        Write-Log -Level 'INFO' "Available assets are: $($releaseInfo.assets.name -join ', ')"
        exit 1
    }
    Write-Log -Level 'SUCCESS' "Found version: $($releaseInfo.tag_name)"
    return [PSCustomObject]@{
        Version      = $releaseInfo.tag_name
        DownloadUrl  = $asset.browser_download_url
        Size         = $asset.size
    }
}

# Function to download the update checker script
function Get-UpdateChecker {
    Write-Log -Level 'INFO' "Downloading update checker script from GitHub..."

    $UpdateScriptUrl = "https://raw.githubusercontent.com/$GithubRepo/master/update-checker.ps1"

    try {
        Invoke-WebRequest -Uri $UpdateScriptUrl -OutFile $UpdateScriptPath -UseBasicParsing
        Write-Log -Level 'SUCCESS' "Update checker script downloaded and installed."
    } catch {
        Write-Log -Level 'ERROR' "Failed to download update checker script: $($_.Exception.Message)"
        exit 1
    }
}

# Function to manage the Scheduled Task for autostart
function Manage-StartupTask {
    param(
        [Parameter(Mandatory = $true)]
        [ValidateSet('Remove', 'Create')]
        [string]$Action,
        [string]$ExecutablePath
    )

    switch ($Action) {
        'Remove' {
            Write-Log -Level 'INFO' "Removing any existing scheduled task named '$AppName'..."
            Unregister-ScheduledTask -TaskName $AppName -Confirm:$false -ErrorAction SilentlyContinue
            Write-Log -Level 'SUCCESS' "Scheduled task cleanup complete."
        }
        'Create' {
            Write-Log -Level 'INFO' "Creating a new scheduled task to run on startup..."

            # Define the action (what program to run and its working directory)
            $taskAction = New-ScheduledTaskAction -Execute $ExecutablePath -WorkingDirectory $InstallDir

            # Define the trigger (when to run it)
            $taskTrigger = New-ScheduledTaskTrigger -AtStartup

            # Define the user and permissions (run as SYSTEM with highest privileges)
            $taskPrincipal = New-ScheduledTaskPrincipal -UserId "NT AUTHORITY\SYSTEM" -LogonType ServiceAccount -RunLevel Highest

            # Define settings (allow it to run indefinitely)
            $taskSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -ExecutionTimeLimit 0

            # Register the task with the system
            Register-ScheduledTask -TaskName $AppName -Action $taskAction -Trigger $taskTrigger -Principal $taskPrincipal -Settings $taskSettings -Description "Runs the Deadlock API Ingest application on system startup."

            Write-Log -Level 'SUCCESS' "Scheduled task created successfully."
        }
    }
}

# Function to manage the update scheduled task
function Manage-UpdateTask {
    param(
        [Parameter(Mandatory = $true)]
        [ValidateSet('Remove', 'Create')]
        [string]$Action
    )

    switch ($Action) {
        'Remove' {
            Write-Log -Level 'INFO' "Removing any existing update scheduled task..."
            Unregister-ScheduledTask -TaskName $UpdateTaskName -Confirm:$false -ErrorAction SilentlyContinue
            Write-Log -Level 'SUCCESS' "Update task cleanup complete."
        }
        'Create' {
            Write-Log -Level 'INFO' "Creating update scheduled task..."

            # Define the action (run PowerShell with the update script)
            $taskAction = New-ScheduledTaskAction -Execute "PowerShell.exe" -Argument "-ExecutionPolicy Bypass -File `"$UpdateScriptPath`"" -WorkingDirectory $InstallDir

            # Define the trigger (daily at 3 AM with random delay)
            $taskTrigger = New-ScheduledTaskTrigger -Daily -At "3:00 AM"
            $taskTrigger.RandomDelay = "PT30M"  # 30 minute random delay

            # Define the user and permissions (run as SYSTEM)
            $taskPrincipal = New-ScheduledTaskPrincipal -UserId "NT AUTHORITY\SYSTEM" -LogonType ServiceAccount -RunLevel Highest

            # Define settings
            $taskSettings = New-ScheduledTaskSettingsSet -AllowStartIfOnBatteries -DontStopIfGoingOnBatteries -ExecutionTimeLimit (New-TimeSpan -Hours 1) -StartWhenAvailable

            # Register the task with the system
            Register-ScheduledTask -TaskName $UpdateTaskName -Action $taskAction -Trigger $taskTrigger -Principal $taskPrincipal -Settings $taskSettings -Description "Daily update checker for Deadlock API Ingest application."

            Write-Log -Level 'SUCCESS' "Update scheduled task created successfully."
            Write-Log -Level 'INFO' "Update task will run daily at 3:00 AM (with up to 30 minute random delay)."
        }
    }
}

# Function to create configuration file
function New-ConfigFile {
    Write-Log -Level 'INFO' "Creating configuration file..."

    $ConfigContent = @"
# Deadlock API Ingest Configuration
# This file controls various settings for the application and updater

# Automatic Updates
# Set to "false" to disable automatic updates
AUTO_UPDATE="true"

# Update Check Time
# The task runs daily at 3 AM, but you can manually trigger updates with:
# Start-ScheduledTask -TaskName $UpdateTaskName

# Backup Retention
# Number of backup versions to keep (default: 5)
BACKUP_RETENTION=5

# Update Log Level
# Options: INFO, WARN, ERROR
UPDATE_LOG_LEVEL="INFO"
"@

    Set-Content -Path $ConfigFile -Value $ConfigContent
    Write-Log -Level 'SUCCESS' "Configuration file created at $ConfigFile"
}

# Function to store version information
function Set-VersionInfo {
    param($Version)
    Set-Content -Path $VersionFile -Value $Version
    Write-Log -Level 'INFO' "Version information stored: $Version"
}

# Function to prompt user for automatic updater setup
function Request-UpdaterInstallation {
    # Check if we're running in an interactive session
    if (-not [Environment]::UserInteractive -or $env:CI -eq 'true' -or $env:AUTOMATED -eq 'true') {
        Write-Log -Level 'INFO' "Non-interactive mode detected. Installing automatic updater by default."
        return $true
    }

    Write-Log -Level 'INFO' "The automatic updater will check for new versions daily and install them automatically."
    Write-Log -Level 'INFO' "This helps keep your installation secure and up-to-date with the latest features."
    Write-Host ""

    $maxAttempts = 2
    $attempts = 0

    while ($attempts -lt $maxAttempts) {
        Write-Host "Would you like to set up automatic updates? (y/n): " -NoNewline -ForegroundColor White

        # Use a job to implement timeout functionality
        $job = Start-Job -ScriptBlock {
            $host.UI.ReadLine()
        }

        $completed = Wait-Job -Job $job -Timeout 10

        if ($completed) {
            $response = Receive-Job -Job $job
            Remove-Job -Job $job

            switch ($response.ToLower().Trim()) {
                { $_ -in @('y', 'yes') } {
                    Write-Log -Level 'INFO' "User chose to install automatic updater."
                    return $true
                }
                { $_ -in @('n', 'no') } {
                    Write-Log -Level 'INFO' "User chose to skip automatic updater installation."
                    return $false
                }
                default {
                    $attempts++
                    if ($attempts -lt $maxAttempts) {
                        Write-Host "Invalid response. Please enter 'y' for yes or 'n' for no." -ForegroundColor Yellow
                    }
                }
            }
        } else {
            Remove-Job -Job $job -Force
            Write-Host ""
            Write-Log -Level 'INFO' "No response received within 10 seconds. Installing automatic updater by default."
            return $true
        }
    }

    Write-Log -Level 'INFO' "Maximum attempts reached. Installing automatic updater by default."
    return $true
}

# --- Main Installation Logic ---

Clear-Content -Path $LogFile -ErrorAction SilentlyContinue
Write-Log -Level 'INFO' "Starting Deadlock API Ingest installation..."
Write-Log -Level 'INFO' "Log file is available at: $LogFile"

Test-IsAdmin
$release = Get-LatestRelease

# Remove any old scheduled tasks (both main and update)
Manage-StartupTask -Action 'Remove'
Manage-UpdateTask -Action 'Remove'

Write-Log -Level 'INFO' "Stopping any currently running instance of '$AppName'..."
Stop-Process -Name $AppName -Force -ErrorAction SilentlyContinue

Write-Log -Level 'INFO' "Creating installation directory: $InstallDir"
New-Item -Path $InstallDir -ItemType Directory -Force | Out-Null
New-Item -Path $BackupDir -ItemType Directory -Force | Out-Null

# Ensure log directory exists
$UpdateLogDir = Split-Path -Parent $UpdateLogFile
New-Item -Path $UpdateLogDir -ItemType Directory -Force | Out-Null

$downloadPath = Join-Path -Path $InstallDir -ChildPath $FinalExecutableName
Write-Log -Level 'INFO' "Downloading $($release.DownloadUrl)..."
Invoke-WebRequest -Uri $release.DownloadUrl -OutFile $downloadPath -UseBasicParsing

# Verify file size
$actualSize = (Get-Item -Path $downloadPath).Length
if ($actualSize -ne $release.Size) {
    Write-Log -Level 'ERROR' "File size mismatch! Expected: $($release.Size) bytes, Got: $actualSize bytes."
    exit 1
}
Write-Log -Level 'SUCCESS' "File integrity verified."

Unblock-File -Path $downloadPath

# Store version information
Set-VersionInfo -Version $release.Version

# Create configuration file
New-ConfigFile

# Create the main scheduled task
Manage-StartupTask -Action 'Create' -ExecutablePath $downloadPath

# Prompt user for automatic updater setup
if (Request-UpdaterInstallation) {
    # Download update checker script
    Get-UpdateChecker

    # Create the update scheduled task
    Manage-UpdateTask -Action 'Create'

    Write-Log -Level 'SUCCESS' "Automatic updater has been installed and configured."
} else {
    Write-Log -Level 'INFO' "Skipping automatic updater installation as requested."
}

Write-Host " "

# Check if update task was created to determine success message
$updateTaskExists = $null -ne (Get-ScheduledTask -TaskName $UpdateTaskName -ErrorAction SilentlyContinue)

if ($updateTaskExists) {
    Write-Log -Level 'SUCCESS' "Deadlock API Ingest ($($release.Version)) has been installed successfully with automatic updates!"
} else {
    Write-Log -Level 'SUCCESS' "Deadlock API Ingest ($($release.Version)) has been installed successfully!"
}

Write-Log -Level 'INFO' "The application will now start automatically every time the computer boots up."
Write-Host " "
Write-Host "You can manage the main task via the Task Scheduler (taskschd.msc) or PowerShell:" -ForegroundColor White
Write-Host "  - Check status:  Get-ScheduledTask -TaskName $AppName | Get-ScheduledTaskInfo" -ForegroundColor Yellow
Write-Host "  - Run manually:  Start-ScheduledTask -TaskName $AppName" -ForegroundColor Yellow
Write-Host "  - Stop it:       Stop-ScheduledTask -TaskName $AppName" -ForegroundColor Yellow
Write-Host " "

if ($updateTaskExists) {
    Write-Host "Automatic update functionality:" -ForegroundColor White
    Write-Host "  - Update task:   Get-ScheduledTask -TaskName $UpdateTaskName | Get-ScheduledTaskInfo" -ForegroundColor Yellow
    Write-Host "  - Manual update: Start-ScheduledTask -TaskName $UpdateTaskName" -ForegroundColor Yellow
    Write-Host "  - Update logs:   Get-Content '$UpdateLogFile'" -ForegroundColor Yellow
    Write-Host "  - Disable updates: Edit '$ConfigFile' and set AUTO_UPDATE=`"false`"" -ForegroundColor Yellow
    Write-Host " "
    Write-Host "Update logs: $UpdateLogFile" -ForegroundColor Cyan
} else {
    Write-Host "To enable automatic updates later, you can re-run this installer." -ForegroundColor White
    Write-Host " "
}

Write-Host "Configuration file: $ConfigFile" -ForegroundColor Cyan
Write-Host "Version file: $VersionFile" -ForegroundColor Cyan
Write-Host " "